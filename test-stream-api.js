/**
 * 测试新的流式聊天API
 */

const testStreamAPI = async () => {
  console.log("🧪 测试流式聊天API...");

  const testData = {
    chatId: "test-chat-id",
    messages: [
      {
        role: "user",
        content: "请帮我创建一个软件工程师的简历，我叫张三，有5年Java开发经验"
      }
    ],
    locale: "zh",
    currentResumeData: null,
    referencedResumeData: []
  };

  try {
    const response = await fetch("http://localhost:3001/api/chat/stream", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(testData),
    });

    console.log("📡 响应状态:", response.status);
    console.log("📋 响应头:", Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ API错误:", errorText);
      return;
    }

    // 检查是否是流式响应
    if (response.headers.get("content-type")?.includes("text/plain")) {
      console.log("✅ 检测到流式响应");
      
      const reader = response.body?.getReader();
      if (reader) {
        console.log("📖 开始读取流数据...");
        
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const chunk = new TextDecoder().decode(value);
          console.log("📦 收到数据块:", chunk);
        }
      }
    } else {
      const text = await response.text();
      console.log("📄 响应内容:", text);
    }

  } catch (error) {
    console.error("💥 测试失败:", error);
  }
};

// 运行测试
testStreamAPI().then(() => {
  console.log("🏁 测试完成");
}).catch(error => {
  console.error("💥 测试异常:", error);
});
