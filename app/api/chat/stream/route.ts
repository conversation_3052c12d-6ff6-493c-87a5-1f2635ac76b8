import { streamText } from "ai";
import { aiConfigWithTools, validateAIConfig } from "@/app/lib/ai-config";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import {
  checkMembershipStatus,
  incrementChatCount,
} from "@/app/lib/membership";
import { createChatPrompt } from "@/app/lib/prompts";
import { prisma } from "@/lib/prisma";

// POST /api/chat/stream - 流式聊天API，支持工具调用
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const {
      chatId,
      messages, // 使用标准的messages格式
      locale = "zh",
      currentResumeData = null,
      referencedResumeData = [],
    } = body;

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return new Response(
        JSON.stringify({ error: "Messages are required" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    if (!chatId) {
      return new Response(
        JSON.stringify({ error: "Chat ID is required" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    // 检查用户认证状态
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return new Response(
        JSON.stringify({ error: "Unauthorized" }),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }

    // 检查会员状态和聊天次数限制
    const membershipStatus = await checkMembershipStatus(session.user.id);
    if (!membershipStatus.canChat) {
      return new Response(
        JSON.stringify({
          error: "Chat limit exceeded",
          membershipRequired: true,
          chatCount: membershipStatus.chatCount,
          remainingChats: membershipStatus.remainingChats,
        }),
        { status: 403, headers: { "Content-Type": "application/json" } }
      );
    }

    // 验证对话是否属于当前用户
    const chat = await prisma.chat.findUnique({
      where: {
        id: chatId,
        userId: session.user.id,
      },
      select: {
        id: true,
        userId: true,
      },
    });

    if (!chat) {
      return new Response(
        JSON.stringify({ error: "Chat not found" }),
        { status: 404, headers: { "Content-Type": "application/json" } }
      );
    }

    // 获取最后一条用户消息
    const lastUserMessage = messages[messages.length - 1];
    if (lastUserMessage?.role === "user") {
      // 保存用户消息到数据库
      await prisma.chatMessage.create({
        data: {
          chatId,
          role: "USER",
          content: lastUserMessage.content,
          metadata:
            referencedResumeData.length > 0
              ? { referencedResumeData }
              : undefined,
        },
      });
    }

    // 验证AI配置
    validateAIConfig();

    // 创建AI提示词
    const systemPrompt = createChatPrompt(
      currentResumeData,
      referencedResumeData,
      locale
    );

    // 构建完整的消息历史
    const fullMessages = [
      {
        role: "system" as const,
        content: systemPrompt,
      },
      ...messages,
    ];

    // 使用Vercel AI SDK生成流式响应
    const result = await streamText({
      model: aiConfigWithTools.model,
      messages: fullMessages,
      temperature: aiConfigWithTools.temperature,
      maxTokens: aiConfigWithTools.maxTokens,
      tools: aiConfigWithTools.tools,
      onFinish: async ({ text, toolCalls }) => {
        try {
          // 保存AI回复到数据库
          await prisma.chatMessage.create({
            data: {
              chatId,
              role: "ASSISTANT",
              content: text,
              metadata: toolCalls && toolCalls.length > 0 ? { toolCalls } : undefined,
            },
          });

          // 增加用户聊天次数
          await incrementChatCount(session.user.id);
        } catch (error) {
          console.error("Failed to save message or update chat count:", error);
        }
      },
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error("Stream Chat API Error:", error);

    return new Response(
      JSON.stringify({
        error: "Internal Server Error",
      }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}
