"use client";

import { useRef, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>, User } from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  MessageContent,
  EnhancedMessageContent,
} from "@/app/components/common/message-content";
import { TypingIndicator } from "./TypingIndicator";

// 使用新的UIMessage类型
interface UIMessage {
  id: string;
  role: "user" | "assistant" | "system";
  parts: Array<{
    type: string;
    text?: string;
    toolName?: string;
    input?: any;
    output?: any;
    state?: string;
    toolCallId?: string;
  }>;
}

interface MessageListV2Props {
  messages: UIMessage[];
  isLoading: boolean;
}

export const MessageListV2 = ({ messages, isLoading }: MessageListV2Props) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isLoading]);

  return (
    <div className="flex-grow overflow-hidden">
      <ScrollArea className="h-full pr-1">
        <div className="space-y-4 py-4 px-6">
          {messages.map((message: UIMessage) => (
            <div
              key={message.id}
              className={`flex items-start gap-3 ${
                message.role === "user" ? "justify-end" : ""
              }`}
            >
              {message.role === "assistant" && (
                <Avatar className="w-8 h-8">
                  <AvatarFallback>
                    <Bot className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
              )}
              <div
                className={`rounded-lg px-3 py-2 max-w-[80%] ${
                  message.role === "user"
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted"
                }`}
              >
                {/* 渲染消息的各个部分 */}
                {message.parts.map((part, partIndex) => {
                  switch (part.type) {
                    case "text":
                      return (
                        <EnhancedMessageContent
                          key={partIndex}
                          content={part.text || ""}
                          enableMarkdown
                        />
                      );
                    case "tool-updateResume":
                      return (
                        <div
                          key={partIndex}
                          className="mt-2 text-xs text-muted-foreground"
                        >
                          <div className="flex items-center gap-1">
                            <span>🔧</span>
                            <span>
                              {part.state === "output-available"
                                ? "已更新简历数据"
                                : part.state === "input-available"
                                ? "正在更新简历..."
                                : "准备更新简历..."}
                            </span>
                          </div>
                          {part.state === "input-available" && part.input && (
                            <pre className="mt-1 text-xs bg-muted/50 p-2 rounded">
                              {JSON.stringify(part.input, null, 2)}
                            </pre>
                          )}
                        </div>
                      );
                    default:
                      // 处理其他工具调用
                      if (part.type?.startsWith("tool-")) {
                        return (
                          <div
                            key={partIndex}
                            className="mt-2 text-xs text-muted-foreground"
                          >
                            <div className="flex items-center gap-1">
                              <span>🔧</span>
                              <span>
                                工具调用: {part.toolName || part.type}
                              </span>
                            </div>
                            {part.input && (
                              <pre className="mt-1 text-xs bg-muted/50 p-2 rounded">
                                {JSON.stringify(part.input, null, 2)}
                              </pre>
                            )}
                          </div>
                        );
                      }
                      return null;
                  }
                })}
              </div>
              {message.role === "user" && (
                <Avatar className="w-8 h-8">
                  <AvatarFallback>
                    <User className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
              )}
            </div>
          ))}
          {isLoading && <TypingIndicator />}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
    </div>
  );
};
