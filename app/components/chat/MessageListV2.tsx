"use client";

import { useRef, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Message } from "ai";
import { <PERSON><PERSON>, User } from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  MessageContent,
  EnhancedMessageContent,
} from "@/app/components/common/message-content";
import { TypingIndicator } from "./TypingIndicator";

interface MessageListV2Props {
  messages: Message[];
  isLoading: boolean;
}

export const MessageListV2 = ({ messages, isLoading }: MessageListV2Props) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isLoading]);

  return (
    <div className="flex-grow overflow-hidden">
      <ScrollArea className="h-full pr-1">
        <div className="space-y-4 py-4 px-6">
          {messages.map((message: Message) => (
            <div
              key={message.id}
              className={`flex items-start gap-3 ${
                message.role === "user" ? "justify-end" : ""
              }`}
            >
              {message.role === "assistant" && (
                <Avatar className="w-8 h-8">
                  <AvatarFallback>
                    <Bot className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
              )}
              <div
                className={`rounded-lg px-3 py-2 max-w-[80%] ${
                  message.role === "user"
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted"
                }`}
              >
                <EnhancedMessageContent
                  content={message.content}
                  enableMarkdown
                />
                
                {/* 显示工具调用信息 */}
                {message.toolInvocations && message.toolInvocations.length > 0 && (
                  <div className="mt-2 text-xs text-muted-foreground">
                    {message.toolInvocations.map((toolCall, index) => (
                      <div key={index} className="flex items-center gap-1">
                        <span>🔧</span>
                        <span>
                          {toolCall.toolName === "updateResume" 
                            ? "已更新简历数据" 
                            : `调用工具: ${toolCall.toolName}`}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              {message.role === "user" && (
                <Avatar className="w-8 h-8">
                  <AvatarFallback>
                    <User className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
              )}
            </div>
          ))}
          {isLoading && <TypingIndicator />}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
    </div>
  );
};
