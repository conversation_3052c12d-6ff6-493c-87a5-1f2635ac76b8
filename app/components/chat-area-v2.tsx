"use client";

import { useChat } from "@ai-sdk/react";
import { useAppStore } from "@/app/store";
import { useEffect, useCallback } from "react";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { useSession } from "next-auth/react";
import { useMembership } from "@/app/hooks/useMembership";
import { ChatHeader } from "./chat/ChatHeader";
import { MessageListV2 } from "./chat/MessageListV2";
import { ChatInputV2 } from "./chat/ChatInputV2";
import {
  isDefaultResumeData,
  syncResumeDataToServer,
  mergeResumeData,
} from "@/app/lib/resume-utils";
import { ResumeSchema } from "@/app/lib/types";

export const ChatAreaV2 = () => {
  const t = useTranslations("ChatArea");
  const locale = useLocale();
  const { data: session } = useSession();
  const { membershipStatus, refreshMembershipStatus } = useMembership();
  const {
    setLoginDialogOpen,
    resumeData: currentResumeData,
    setResumeData,
    currentChatId,
    createNewChat,
    openMembershipDialog,
  } = useAppStore();

  // 使用Vercel AI SDK的useChat hook
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    append,
    setMessages,
  } = useChat({
    api: "/api/chat/stream",
    body: {
      chatId: currentChatId,
      locale,
      currentResumeData: !isDefaultResumeData(currentResumeData)
        ? currentResumeData
        : null,
      referencedResumeData: [], // TODO: 实现引用功能
    },
    async onToolCall({ toolCall }) {
      // 处理工具调用
      if (toolCall.toolName === "updateResume") {
        const updatedData = toolCall.args as Partial<ResumeSchema>;
        console.log("Tool call received:", updatedData);

        // 合并简历数据
        const finalResumeData = mergeResumeData(currentResumeData, updatedData);
        setResumeData(finalResumeData);

        // 同步到服务器
        if (currentChatId && !isDefaultResumeData(finalResumeData)) {
          await syncResumeDataToServer(currentChatId, finalResumeData);
        }

        return {
          success: true,
          message: "简历数据已更新",
        };
      }

      return {
        success: false,
        message: "未知的工具调用",
      };
    },
    onFinish: () => {
      // 刷新会员状态
      refreshMembershipStatus(true);
    },
    onError: (error) => {
      console.error("Chat error:", error);
      // 处理错误，如会员限制等
      if (error.message.includes("Chat limit exceeded")) {
        openMembershipDialog();
      }
    },
  });

  // 自定义发送消息函数，支持引用功能
  const handleSendMessage = useCallback(
    async (userInput: string, referencedChats?: string[]) => {
      if (!session?.user) {
        setLoginDialogOpen(true);
        return;
      }

      if (membershipStatus && !membershipStatus.canChat) {
        openMembershipDialog();
        return;
      }

      let chatId = currentChatId;

      // 如果没有当前对话，创建新对话
      if (!chatId) {
        chatId = await createNewChat("新对话", currentResumeData || undefined);
        if (!chatId) {
          console.error("Failed to create new chat");
          return;
        }
      }

      // 使用useChat的append方法发送消息
      await append({
        role: "user",
        content: userInput,
      });
    },
    [
      session?.user,
      membershipStatus,
      currentChatId,
      currentResumeData,
      createNewChat,
      setLoginDialogOpen,
      openMembershipDialog,
      append,
    ]
  );

  // 当切换对话时，加载对话历史
  useEffect(() => {
    const loadChatHistory = async () => {
      if (currentChatId) {
        try {
          const response = await fetch(`/api/chats/${currentChatId}`);
          if (response.ok) {
            const { data: chat } = await response.json();

            // 转换消息格式
            const chatMessages = chat.messages.map((msg: any) => ({
              id: msg.id,
              role: msg.role.toLowerCase(),
              content: msg.content,
              createdAt: new Date(msg.createdAt),
            }));

            setMessages(chatMessages);
          }
        } catch (error) {
          console.error("Failed to load chat history:", error);
        }
      } else {
        // 清空消息历史
        setMessages([]);
      }
    };

    loadChatHistory();
  }, [currentChatId, setMessages]);

  return (
    <div className="h-full flex flex-col">
      <ChatHeader />
      <MessageListV2 messages={messages} isLoading={isLoading} />
      <ChatInputV2
        input={input}
        handleInputChange={handleInputChange}
        handleSubmit={handleSubmit}
        isLoading={isLoading}
        onSendMessage={handleSendMessage}
      />
    </div>
  );
};
