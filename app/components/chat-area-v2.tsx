"use client";

import { useChat } from "@ai-sdk/react";

import { useAppStore } from "@/app/store";
import { useEffect, useCallback, useState } from "react";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { useSession } from "next-auth/react";
import { useMembership } from "@/app/hooks/useMembership";
import { ChatHeader } from "./chat/ChatHeader";
import { MessageListV2 } from "./chat/MessageListV2";
import { ChatInputV2 } from "./chat/ChatInputV2";
import {
  isDefaultResumeData,
  syncResumeDataToServer,
  mergeResumeData,
} from "@/app/lib/resume-utils";
import { ResumeSchema } from "@/app/lib/types";

export const ChatAreaV2 = () => {
  const t = useTranslations("ChatArea");
  const locale = useLocale();
  const { data: session } = useSession();
  const { membershipStatus, refreshMembershipStatus } = useMembership();
  const {
    setLoginDialogOpen,
    resumeData: currentResumeData,
    setResumeData,
    currentChatId,
    createNewChat,
    openMembershipDialog,
  } = useAppStore();

  const [input, setInput] = useState("");

  // 使用Vercel AI SDK的useChat hook
  const { messages, sendMessage, status, error } = useChat({
    onFinish: () => {
      // 刷新会员状态
      refreshMembershipStatus(true);
    },
    onError: (error) => {
      console.error("Chat error:", error);
      // 处理错误，如会员限制等
      if (error.message.includes("Chat limit exceeded")) {
        openMembershipDialog();
      }
    },
  });

  // 自定义发送消息函数，支持引用功能
  const handleSendMessage = useCallback(
    async (userInput: string, referencedChats?: string[]) => {
      if (!session?.user) {
        setLoginDialogOpen(true);
        return;
      }

      if (membershipStatus && !membershipStatus.canChat) {
        openMembershipDialog();
        return;
      }

      let chatId = currentChatId;

      // 如果没有当前对话，创建新对话
      if (!chatId) {
        chatId = await createNewChat("新对话", currentResumeData || undefined);
        if (!chatId) {
          console.error("Failed to create new chat");
          return;
        }
      }

      // 使用sendMessage发送消息
      await sendMessage(
        { text: userInput },
        {
          body: {
            chatId,
            locale,
            currentResumeData: !isDefaultResumeData(currentResumeData)
              ? currentResumeData
              : null,
            referencedResumeData: referencedChats || [],
          },
        }
      );
    },
    [
      session?.user,
      membershipStatus,
      currentChatId,
      currentResumeData,
      createNewChat,
      setLoginDialogOpen,
      openMembershipDialog,
      sendMessage,
      locale,
    ]
  );

  // 处理工具调用结果
  useEffect(() => {
    // 监听消息中的工具调用
    const lastMessage = messages[messages.length - 1];
    if (lastMessage?.role === "assistant" && lastMessage.parts) {
      for (const part of lastMessage.parts) {
        if (
          part.type?.startsWith("tool-") &&
          part.type === "tool-updateResume"
        ) {
          // 处理简历更新工具调用
          if (part.state === "output-available" && part.input) {
            const updatedData = part.input as Partial<ResumeSchema>;
            console.log("Tool call received:", updatedData);

            // 合并简历数据
            const finalResumeData = mergeResumeData(
              currentResumeData,
              updatedData
            );
            setResumeData(finalResumeData);

            // 同步到服务器
            if (currentChatId && !isDefaultResumeData(finalResumeData)) {
              syncResumeDataToServer(currentChatId, finalResumeData);
            }
          }
        }
      }
    }
  }, [messages, currentResumeData, setResumeData, currentChatId]);

  const isLoading = false; // TODO: 从useChat hook获取正确的loading状态

  return (
    <div className="h-full flex flex-col">
      <ChatHeader />
      <MessageListV2 messages={messages} isLoading={isLoading} />
      <ChatInputV2
        input={input}
        setInput={setInput}
        isLoading={isLoading}
        onSendMessage={handleSendMessage}
      />
    </div>
  );
};
