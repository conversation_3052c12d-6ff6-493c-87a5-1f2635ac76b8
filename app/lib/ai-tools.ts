import { tool } from "ai";
import { z } from "zod";

// 定义简历更新工具的参数schema
const updateResumeSchema = z.object({
  personalInfo: z.object({
    name: z.string().optional(),
    phone: z.string().optional(),
    email: z.string().optional(),
    linkedin: z.string().optional(),
  }).optional(),
  summary: z.string().optional(),
  experience: z.array(z.object({
    id: z.string(),
    jobTitle: z.string(),
    company: z.string(),
    startDate: z.string(),
    endDate: z.string(),
    responsibilities: z.array(z.string()),
  })).optional(),
  education: z.array(z.object({
    id: z.string(),
    degree: z.string(),
    major: z.string(),
    school: z.string(),
    startDate: z.string(),
    graduationDate: z.string(),
  })).optional(),
  skills: z.array(z.object({
    name: z.string(),
    data: z.array(z.string()),
  })).optional(),
  projects: z.array(z.object({
    id: z.string(),
    name: z.string(),
    startDate: z.string(),
    endDate: z.string(),
    responsibilities: z.array(z.string()),
    technologies: z.array(z.string()),
  })).optional(),
});

// 定义简历更新工具
export const updateResumeTool = tool({
  description: `更新用户的简历数据。当用户要求修改、添加或删除简历内容时使用此工具。
  只需要传递需要更新的字段，不需要传递完整的简历数据。
  例如：如果只需要更新个人信息，只传递personalInfo字段。`,
  parameters: updateResumeSchema,
  execute: async (params) => {
    // 这里只是返回更新的数据，实际的更新逻辑在前端处理
    return {
      success: true,
      updatedData: params,
      message: "简历数据已更新",
    };
  },
});

// 导出所有工具
export const aiTools = {
  updateResume: updateResumeTool,
};

// 工具调用类型定义
export type ToolCallResult = {
  success: boolean;
  updatedData: any;
  message: string;
};
