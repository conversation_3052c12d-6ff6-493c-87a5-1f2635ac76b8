import { createOpenAI } from "@ai-sdk/openai";
import { aiTools } from "./ai-tools";

// Create Volcano Engine OpenAI-compatible client
const volcanoOpenAI = createOpenAI({
  apiKey: process.env.OPENAI_API_KEY!,
  baseURL:
    process.env.OPENAI_BASE_URL || "https://ark.cn-beijing.volces.com/api/v3",
});

// AI Configuration for Volcano Engine (OpenAI-compatible)
export const aiConfig = {
  model: volcanoOpenAI(process.env.OPENAI_MODEL || "deepseek-v3-250324"),
  temperature: 0,
  maxTokens: 2000,
};

// AI Configuration with tools for streaming chat
export const aiConfigWithTools = {
  model: volcanoOpenAI(process.env.OPENAI_MODEL || "deepseek-v3-250324"),
  temperature: 0,
  maxTokens: 2000,
  tools: aiTools,
};

// Helper function to validate environment variables
export function validateAIConfig() {
  if (!process.env.OPENAI_API_KEY) {
    throw new Error(
      "OPENAI_API_KEY is not configured. Please add your Volcano Engine API key to your .env.local file."
    );
  }
}
